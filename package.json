{"name": "rsi-svelte-v2", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.22.0", "@libsql/client": "^0.14.0", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@types/node": "^22", "drizzle-kit": "^0.30.2", "drizzle-orm": "^0.40.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.0.0", "vite": "^7.0.4"}, "dependencies": {"@node-rs/argon2": "^2.0.2", "bootstrap-icons": "^1.13.1"}}