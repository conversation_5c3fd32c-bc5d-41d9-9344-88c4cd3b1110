<script>
    import { goto } from '$app/navigation';
    import '../styles/card-title.css';

    let { customer = null } = $props(); // may be an object or null

    let notes = $state([]);
    let loading = $state(false);
    let error = $state('');

    // derive id from the passed customer
    let customerId = $derived(customer?.Id ?? customer?.id ?? null);

    async function tryFetch(url) {
        const res = await fetch(url);
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        let data = await res.json();
        if (typeof data === 'string') {
            try { data = JSON.parse(data); } catch (e) {}
        }
        return data;
    }

    async function loadNotes() {
        if (!customerId) return;
        loading = true;
        error = '';
        notes = [];
        try {
            // try a couple of reasonable endpoints
            let data;
                data = await tryFetch(`/api/customers/${customerId}/notes`);

            // normalize array
            if (!Array.isArray(data)) data = data ? [data] : [];
            notes = data;
        } catch (err) {
            console.error('Failed to load notes', err);
            error = err?.message || String(err) || 'Failed to load notes';
            notes = [];
        } finally {
            loading = false;
        }
    }

    function onEdit() {
        if (!customerId) return;
        goto(`/admins/CustomerNoteAdd/${customerId}`);
    }

    // Effect to load notes when customerId changes
    $effect(() => {
        if (customerId) {
            loadNotes();
        } else {
            notes = [];
            loading = false;
            error = '';
        }
    });
</script>

{#if !customerId}
    <div>No Notes</div>
{:else}
    {#if loading}
        <div class="text-center my-2">
            <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
        </div>
    {:else if error}
        <div class="alert alert-danger">{error}</div>
    {:else}
        <div class="card">
            <h5 class="card-title">Dealer/Admin Notes</h5>
            <div class="card-body">
                <div class="mb-3">
                    <button class="btn btn-primary" onclick={onEdit}>Add Note</button>
                </div>
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>Entry Date</th>
                    <th>Body</th>
                </tr>
            </thead>
            <tbody>
                {#if notes.length === 0}
                    <tr>
                        <td colspan="2">No notes for this customer.</td>
                    </tr>
                {:else}
                    {#each notes as n}
                        <tr>
                            <td>{n.EntryDate ?? n.entryDate ?? n.Date ?? ''}</td>
                            <td>{n.Body ?? n.body ?? ''}</td>
                        </tr>
                    {/each}
                {/if}
            </tbody>
        </table>
        </div>
        </div>
    {/if}
{/if}
