<script>
    import { onMount } from 'svelte';

    export let customer = null; // may be an object or null

    let visits = [];
    let loading = false;
    let error = '';

    // tooltip state
    let tooltipVisible = false;
    let tooltipText = '';
    let tooltipX = 0;
    let tooltipY = 0;
    let hoverTimer = null;

    // derive id from the passed customer
    $: customerId = customer?.Id ?? customer?.id ?? null;

    async function tryFetch(url) {
        const res = await fetch(url);
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        let data = await res.json();
        if (typeof data === 'string') {
            try { data = JSON.parse(data); } catch (e) {}
        }
        return data;
    }

    async function loadVisits() {
        if (!customerId) return;
        loading = true;
        error = '';
        visits = [];
        try {
            // primary endpoint for visits
            let data = await tryFetch(`/api/customers/${customerId}/visits`);

            // normalize array
            if (!Array.isArray(data)) data = data ? [data] : [];
            visits = data;
        } catch (err) {
            console.error('Failed to load visits', err);
            error = err?.message || String(err) || 'Failed to load visits';
            visits = [];
        } finally {
            loading = false;
        }
    }

    // load on mount and whenever customerId changes
    onMount(() => {
        if (customerId) loadVisits();
    });

    $: if (customerId) {
        // reactive: if id changes, reload
        loadVisits();
    } else {
        visits = [];
        loading = false;
        error = '';
    }

    // helper to show tooltip after a short delay
    function startHover(e, comments) {
        // position near cursor
        const { clientX, clientY } = e;
        tooltipX = clientX + 10;
        tooltipY = clientY + 10;
        tooltipText = comments ?? '';
        clearHoverTimer();
        hoverTimer = setTimeout(() => {
            tooltipVisible = true;
        }, 500);
    }

    function moveHover(e) {
        tooltipX = e.clientX + 10;
        tooltipY = e.clientY + 10;
    }

    function clearHoverTimer() {
        if (hoverTimer) {
            clearTimeout(hoverTimer);
            hoverTimer = null;
        }
    }

    function endHover() {
        clearHoverTimer();
        tooltipVisible = false;
        tooltipText = '';
    }

    // cleanup on destroy
    import { onDestroy } from 'svelte';
    onDestroy(() => {
        clearHoverTimer();
    });
</script>



{#if !customerId}
    <div>No visits</div>
{:else}
    {#if loading}
        <div class="text-center my-2">
            <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
        </div>
    {:else if error}
        <div class="alert alert-danger">{error}</div>
    {:else}
        <div class="card">
        <div class="card-body">
            <h5 class="card-title">Visits</h5>
            <div class="mb-3">
   <button class="btn btn-primary" on:click={onEdit}>Add Visit</button>
</div>
        <table class="table table-sm table-striped">
            <thead>
                <tr>
                    <th colspan="3">Actions</th>
                    <th>Visit Date</th>
                    <th>Dealer</th>
                </tr>

            </thead>
            <tbody>
                {#if visits.length === 0}
                    <tr>
                        <td>No visits for this customer.</td>
                    </tr>
                {:else}
                    {#each visits as e}
                        <tr>
                            <td class="bi bi-eye" on:click={() => goto(`/admins/CustomerVisits/${visits.id}`)}></td>
                            <td class="bi bi-pencil"></td>
                            <td class="bi bi-trash"></td>
                            <td
                                tabindex="0"
                                on:mouseenter={(ev) => startHover(ev, e.Comments ?? e.comments ?? '')}
                                on:mousemove={(ev) => moveHover(ev)}
                                on:mouseleave={() => endHover()}
                                on:focus={(ev) => startHover(ev, e.Comments ?? e.comments ?? '')}
                                on:blur={() => endHover()}
                                >{e.VisitDate}</td>
                            <td>{e.DealerName}</td>
                        </tr>
                    {/each}
                {/if}
            </tbody>
        </table>
        {#if tooltipVisible}
            <div class="visit-tooltip" style="left: {tooltipX}px; top: {tooltipY}px;">{tooltipText}</div>
        {/if}
        </div>
        </div>
    {/if}
{/if}

<style>
.visit-tooltip {
    position: fixed;
    z-index: 3000;
    max-width: 320px;
    background: rgba(0,0,0,0.85);
    color: #fff;
    padding: 8px 10px;
    border-radius: 6px;
    box-shadow: 0 6px 18px rgba(0,0,0,0.2);
    font-size: 0.9rem;
}

td[tabindex] { outline: none; }
td[tabindex]:focus { box-shadow: inset 0 0 0 2px rgba(0,123,255,0.25); }
</style>
