<script>
    import { onMount } from 'svelte';

    let customers = [];
    let loading = true;
    let error = '';

    async function fetchCustomers() {
        try {
            console.log('Fetching customers from /api/customers...');
            const res = await fetch('/api/customers');
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            const data = await res.json();
            console.log('Raw customers data length:', data.length);

            // Sort by ID in descending order and take the top 5
            const sortedCustomers = data.sort((a, b) => b.Id - a.Id);
            const topCustomers = sortedCustomers.slice(0, 5);

            customers = topCustomers;
            console.log('Newest Customers', customers);
        } catch (err) {
            console.error('Failed to fetch customers:', err);
            error = `Failed to load customers: ${err.message}`;
        } finally {
            loading = false;
        }
    }

    onMount(() => {
        fetchCustomers();
    });
</script>


<div class="card">
    <div class="card-body">
        <h5 class="card-title">Newest Customers</h5>

        {#if loading}
            <div class="text-center my-3">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        {:else if error}
            <div class="alert alert-danger" role="alert">
                {error}
            </div>
        {:else}
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>City</th>
                        <th>State</th>
                        <th>Region ID</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    {#if customers.length === 0}
                        <tr>
                            <td colspan="5" class="text-center text-muted">No customers found</td>
                        </tr>
                    {:else}
                        {#each customers as customer}
                            <tr>
                                <td><a href="/admins/CustomerDetail/{customer.Id}">{customer.Name}</a></td>
                                <td>{customer.City}</td>
                                <td>{customer.State}</td>
                                <td>{customer.Region_Id}</td>
                                <td>
                                    {#if customer.Notes}
                                        <span title="{customer.Notes}">
                                            {customer.Notes.length > 50 ? customer.Notes.substring(0, 50) + '...' : customer.Notes}
                                        </span>
                                    {:else}
                                        <span class="text-muted">No notes</span>
                                    {/if}
                                </td>
                            </tr>
                        {/each}
                    {/if}
                </tbody>
            </table>
        {/if}
    </div>
</div>


