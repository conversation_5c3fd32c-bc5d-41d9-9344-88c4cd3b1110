<script>
    import { onMount, onD<PERSON>roy, createEventDispatcher } from 'svelte';
    import { fade } from 'svelte/transition';
    import CustomerCard from './CustomerCard.svelte';

    export let customer = null;
    export let open = false;
    const dispatch = createEventDispatcher();

    function close() {
        dispatch('close');
    }

    function onKey(e) {
        if (e.key === 'Escape') close();
    }

    function onOverlayClick(e) {
        if (e.target === e.currentTarget) close();
    }

    function onBackdropKey(e) {
        // allow Enter or Space to close when backdrop focused
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            close();
        }
    }

    onMount(() => window.addEventListener('keydown', onKey));
    onDestroy(() => window.removeEventListener('keydown', onKey));
</script>

{#if open}
    <!-- backdrop (separate element so its opacity doesn't affect modal content) -->
    <div class="modal-backdrop show" style="position:fixed;inset:0;z-index:1040;background:#fff;" on:click={onOverlayClick} role="presentation" transition:fade>
    </div>

    <!-- modal dialog sits above the backdrop -->
    <div class="modal-dialog modal-lg" style="position:fixed;left:0;right:0;top:80px;margin:auto;z-index:2000;" role="dialog" aria-modal="true" tabindex="-1" on:keydown={onBackdropKey} transition:fade={{ duration: 200 }}>
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Customer</h5>
                <button type="button" class="btn-close" aria-label="Close" on:click={close}></button>
            </div>
            <div class="modal-body">
                <CustomerCard {customer} />
            </div>
        </div>
    </div>
{/if}

<style>
    /* minimal styles to ensure modal centers */
    .modal-dialog { max-width: 800px; }
    .modal-content {
        background: #fff;
        color: #212529;
        padding: 0;
        border-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,1);
        overflow: hidden;
    }
    .modal-body { padding: 1rem; }
</style>