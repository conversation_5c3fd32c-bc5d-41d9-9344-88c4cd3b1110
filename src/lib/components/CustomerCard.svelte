<script>
    import '../styles/card-title.css';
    export let customer = null;
</script>

{#if customer}
    <div class="card">
        <h5 class="card-title">Customer Information</h5>
        <div class="card-body">
            <p class="card-text">
                <strong>Address:</strong>
                {customer.Address1 ?? customer.address1 ?? ''}
                {#if (customer.Address2 ?? customer.address2)}
                    , {customer.Address2 ?? customer.address2}
                {/if}
                <br />
                {customer.City ?? customer.city ?? ''}{#if (customer.State ?? customer.state)} , {/if}{customer.State ?? customer.state ?? ''} {customer.ZipCode ?? customer.zipCode ?? ''}
            </p>
            <p class="card-text"><strong>Phone:</strong> {customer.PhoneNumber ?? customer.phoneNumber ?? ''}</p>
        </div>
    </div>
{:else}
    <div class="alert alert-warning">Customer not found.</div>
{/if}


