<script>
    let { id = null } = $props(); // Accept ID as a parameter

    let visit = $state(null);
    let loading = $state(true);
    let error = $state('');

    async function loadVisit() {
        if (!id) {
            error = 'No visit ID provided';
            loading = false;
            return;
        }

        loading = true;
        error = '';
        visit = null;

        try {
            const res = await fetch(`/api/visits/${id}`);
            if (!res.ok) throw new Error(`HTTP ${res.status}`);

            let data = await res.json();
            if (typeof data === 'string') {
                try { data = JSON.parse(data); } catch (e) { /* ignore */ }
            }

            // API sometimes returns an array with one object — normalize to a single visit object
            if (Array.isArray(data)) {
                visit = data[0] ?? null;
            } else {
                visit = data ?? null;
            }
        } catch (err) {
            console.error('Failed to load visit', err);
            error = err?.message || String(err) || 'Failed to load visit';
            visit = null;
        } finally {
            loading = false;
        }
    }

    // Effect to load visit when ID changes
    $effect(() => {
        if (id) {
            loadVisit();
        } else {
            visit = null;
            loading = false;
            error = 'No visit ID provided';
        }
    });

    function formatDate(dateString) {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (e) {
            return dateString; // Return original if parsing fails
        }
    }
</script>

{#if loading}
    <div class="card">
        <div class="card-body text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading visit details...</span>
            </div>
        </div>
    </div>
{:else if error}
    <div class="card">
        <div class="card-body">
            <div class="alert alert-danger">{error}</div>
        </div>
    </div>
{:else if visit}
    <div class="card">
        <div class="card-body">
            <h5 class="card-title">Visit Details</h5>

            <div class="mb-3">
                <strong>Visit Date:</strong>
                <p class="card-text mt-1">
                    {formatDate(visit.VisitDate ?? visit.visitDate ?? visit.Date)}
                </p>
            </div>

            <div class="mb-3">
                <strong>Comments:</strong>
                <p class="card-text mt-1">
                    {#if visit.Comments ?? visit.comments}
                        {visit.Comments ?? visit.comments}
                    {:else}
                        <span class="text-muted">No comments available</span>
                    {/if}
                </p>
            </div>
        </div>
    </div>
{:else}
    <div class="card">
        <div class="card-body">
            <div class="alert alert-warning">Visit not found.</div>
        </div>
    </div>
{/if}
