<script>
    import { onMount } from 'svelte';

    export let customer = null; // may be an object or null

    let equipment = [];
    let loading = false;
    let error = '';

    // derive id from the passed customer
    $: customerId = customer?.Id ?? customer?.id ?? null;

    async function tryFetch(url) {
        const res = await fetch(url);
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        let data = await res.json();
        if (typeof data === 'string') {
            try { data = JSON.parse(data); } catch (e) {}
        }
        return data;
    }

    async function loadEquipment() {
        if (!customerId) return;
        loading = true;
        error = '';
        equipment = [];
        try {
            // primary endpoint for equipment
            let data = await tryFetch(`/api/customers/${customerId}/equipment`);

            // normalize array
            if (!Array.isArray(data)) data = data ? [data] : [];
            equipment = data;
        } catch (err) {
            console.error('Failed to load equipment', err);
            error = err?.message || String(err) || 'Failed to load equipment';
            equipment = [];
        } finally {
            loading = false;
        }
    }

    // load on mount and whenever customerId changes
    onMount(() => {
        if (customerId) loadEquipment();
    });

    $: if (customerId) {
        // reactive: if id changes, reload
        loadEquipment();
    } else {
        equipment = [];
        loading = false;
        error = '';
    }
</script>

{#if !customerId}
    <div>No Equipment</div>
{:else}
    {#if loading}
        <div class="text-center my-2">
            <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
        </div>
    {:else if error}
        <div class="alert alert-danger">{error}</div>
    {:else}
        <div class="card">
        <div class="card-body">
            <h5 class="card-title">Equipment</h5>
            <div class="mb-3">
            <button class="btn btn-primary" on:click={onEdit}>Add Equipment</button>
            </div>
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>Actions</th>
                    <th>Equipment Name</th>
                </tr>

            </thead>
            <tbody>
                {#if equipment.length === 0}
                    <tr>
                        <td>No equipment for this customer.</td>
                    </tr>
                {:else}
                    {#each equipment as e}
                        <tr>
                        
                             <td>   
                            <button class="btn btn-sm btn-warning" title="Edit">
                                <i class="bi bi-pencil"></i>
                            </button>
                      
                            <button class="btn btn-sm btn-danger" title="Delete">
                                <i class="bi bi-trash"></i>
                            </button>
                            </td>
                            <td>{e.equipment ?? e.equipment ?? e.Address ?? e.address ?? ''}</td>
                        </tr>
                    {/each}
                {/if}
            </tbody>
        </table>
        </div>
        </div>
    {/if}
{/if}
