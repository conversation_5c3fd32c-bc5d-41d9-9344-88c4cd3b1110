<script>
    import { onMount } from 'svelte';

    export let customer = null; // may be an object or null

    let emails = [];
    let loading = false;
    let error = '';

    // derive id from the passed customer
    $: customerId = customer?.Id ?? customer?.id ?? null;

    async function tryFetch(url) {
        const res = await fetch(url);
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        let data = await res.json();
        if (typeof data === 'string') {
            try { data = JSON.parse(data); } catch (e) {}
        }
        return data;
    }

    async function loadEmails() {
        if (!customerId) return;
        loading = true;
        error = '';
        emails = [];
        try {
            // primary endpoint for emails
            let data = await tryFetch(`/api/customers/${customerId}/emails`);

            // normalize array
            if (!Array.isArray(data)) data = data ? [data] : [];
            emails = data;
        } catch (err) {
            console.error('Failed to load emails', err);
            error = err?.message || String(err) || 'Failed to load emails';
            emails = [];
        } finally {
            loading = false;
        }
    }

    // load on mount and whenever customerId changes
    onMount(() => {
        if (customerId) loadEmails();
    });

    $: if (customerId) {
        // reactive: if id changes, reload
        loadEmails();
    } else {
        emails = [];
        loading = false;
        error = '';
    }
</script>

{#if !customerId}
    <div>No Emails</div>
{:else}
    {#if loading}
        <div class="text-center my-2">
            <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
        </div>
    {:else if error}
        <div class="alert alert-danger">{error}</div>
    {:else}
        <div class="card">
        <div class="card-body">
            <h5 class="card-title">Email Addresses</h5>
            <div class="mb-3">
            <button class="btn btn-primary" on:click={onEdit}>Add Email Address</button>
            </div>
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>Email</th>
                </tr>
            </thead>
            <tbody>
                {#if emails.length === 0}
                    <tr>
                        <td>No email addresses for this customer.</td>
                    </tr>
                {:else}
                    {#each emails as e}
                        <tr>
                            <td>{e.Email ?? e.email ?? e.Address ?? e.address ?? ''}</td>
                        </tr>
                    {/each}
                {/if}
            </tbody>
        </table>
        </div>
        </div>
    {/if}
{/if}
