<script>
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';

  let id;
  $: id = $page?.params?.id;

  let loading = true;
  let error = '';
  let saving = false;
  let success = '';

  // form fields
  let firstName = '';
  let lastName = '';
  let email = '';
  let phone = '';
  let password = '';

  // regions state
  let regions = [];
  let regionsLoading = true;
  let regionsError = '';
  // selected region ids (array) - checkbox group binding
  let selectedRegionIds = [];

  function normalizeDealer(d) {
    return {
      firstName: d.FirstName ?? d.firstName ?? d.First ?? d.first ?? '',
      lastName: d.LastName ?? d.lastName ?? d.Last ?? d.last ?? '',
      email: d.Email ?? d.email ?? '',
      phone: d.PhoneNumber ?? d.phoneNumber ?? d.Phone ?? d.phone ?? '',
      // derive an array of region ids if present
      regionIds: (d.Regions ?? d.regions ?? []).map(r => r?.Id ?? r?.id ?? r) ?? [],
      password: '' // don't prefill password for security; backend may not provide it
    };
  }

 async function loadRegions() {
    regionsLoading = true;
    regionsError = '';
    try {
      const res = await fetch('/api/regions');
      if (!res.ok) throw new Error(`Failed to load regions (${res.status})`);
      let data = await res.json();
      if (!Array.isArray(data)) {
        data = data?.results ?? data?.items ?? [data];
      }
      regions = (data || []).map(r => ({
        id: r.Id ?? r.id,
        name: r.Name ?? r.name ?? String(r)
      }));
    } catch (err) {
      console.error(err);
      regionsError = err?.message ?? 'Failed to load regions';
      regions = [];
    } finally {
      regionsLoading = false;
    }
  }

   async function loadDealerRegions() {
    if (!id) return;
    try {
      const res = await fetch(`/api/dealerregions/${id}`);
      if (!res.ok) {
        if (res.status === 404) {
          selectedRegionIds = [];
          return;
        }
        throw new Error(`Failed to load dealer regions (${res.status})`);
      }
      let data = await res.json();
      if (!Array.isArray(data)) data = data?.results ?? data?.items ?? [data];
      // normalize region ids to strings so they match checkbox values
      selectedRegionIds = (data || [])
        .map(r => r.region_id ?? r.RegionId ?? r.regionId ?? r.Region_id ?? r.region_id)
        .filter(Boolean)
        .map(String);
    } catch (err) {
      console.error(err);
      // keep selectedRegionIds empty on error
      selectedRegionIds = [];
      regionsError = regionsError || (err?.message ?? 'Failed to load dealer regions');
    }
  }

  async function loadDealer() {
    if (!id) return;
    loading = true;
    error = '';
    try {
      const res = await fetch(`/api/dealers/${id}`);
      if (!res.ok) throw new Error(`Failed to fetch dealer (${res.status})`);
      let data = await res.json();
      // backend may return array or single object
      if (Array.isArray(data)) data = data[0] ?? null;
      if (!data) {
        error = 'Dealer not found';
        return;
      }
      const n = normalizeDealer(data);
      firstName = n.firstName;
      lastName = n.lastName;
      email = n.email;
      phone = n.phone;
      // set selected regions from the dealer record (if any)
      selectedRegionIds = Array.isArray(n.regionIds) ? n.regionIds.filter(Boolean).map(String) : [];
   
    } catch (err) {
      console.error(err);
      error = err?.message ?? 'Failed to load dealer';
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    loadDealer();
    loadRegions();
    loadDealerRegions();
  });

  async function save() {
    if (!id) return;
    saving = true;
    error = '';
    success = '';
    const payload = {
      FirstName: firstName,
      LastName: lastName,
      Email: email,
      PhoneNumber: phone,
      Password: password || undefined,
      Regions: selectedRegionIds // send selected region ids to backend (adjust key if backend expects different)
    };
    try {
      const res = await fetch(`/api/dealers/${id}`, {
        method: 'PUT', // adjust to backend expectation (PATCH/POST) if needed
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      if (!res.ok) {
        const text = await res.text();
        throw new Error(text || `Save failed (${res.status})`);
      }
      success = 'Dealer saved successfully.';
      // optionally navigate back after save:
      // goto('/admins/DealerList');
    } catch (err) {
      console.error(err);
      error = err?.message ?? 'Save failed';
    } finally {
      saving = false;
    }
  }

  function cancel() {
    goto('/admins/DealerList');
  }

</script>

<div class="container mt-4">
  <h1 class="mb-3">Edit Dealer</h1>

  {#if loading}
    <div class="text-center my-4">
      <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
    </div>
  {:else}
    {#if error}
      <div class="alert alert-danger">{error}</div>
    {/if}

    {#if !error}
      <form on:submit|preventDefault={save} class="needs-validation" novalidate>
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="firstName" class="form-label">First Name</label>
            <input id="firstName" class="form-control" bind:value={firstName} required />
          </div>

          <div class="col-md-6 mb-3">
            <label for="lastName" class="form-label">Last Name</label>
            <input id="lastName" class="form-control" bind:value={lastName} required />
          </div>

          <div class="col-md-6 mb-3">
            <label for="email" class="form-label">Email</label>
            <input id="email" type="email" class="form-control" bind:value={email} />
          </div>

          <div class="col-md-6 mb-3">
            <label for="phone" class="form-label">Phone Number</label>
            <input id="phone" class="form-control" bind:value={phone} />
          </div>

          <div class="col-12 mb-3">
            <label for="password" class="form-label">Password</label>
            <input id="password" type="password" class="form-control" bind:value={password} placeholder="Enter new password to change" />
            <div class="form-text">Leave blank to keep existing password.</div>
          </div>
        </div>

        <!-- insert into the form markup (for example after phone/password fields) -->
<div class="col-12 mb-3">
  <label class="form-label">Regions</label>

  {#if regionsLoading}
    <div class="text-center py-2">
      <div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>
    </div>
  {:else if regionsError}
    <div class="alert alert-danger">{regionsError}</div>
  {:else if regions.length === 0}
    <div class="form-text">No regions available.</div>
  {:else}
    <div class="row">
      <!-- First Column -->
      <div class="col-12 col-md-6">
        {#each regions.slice(0, Math.ceil(regions.length / 2)) as r}
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id={"region-" + r.id}
              value={String(r.id)}
              bind:group={selectedRegionIds} />
            <label class="form-check-label" for={"region-" + r.id}>
              {r.name}
            </label>
          </div>
        {/each}
      </div>

      <!-- Second Column -->
      <div class="col-12 col-md-6">
        {#each regions.slice(Math.ceil(regions.length / 2)) as r}
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id={"region-" + r.id}
              value={String(r.id)}
              bind:group={selectedRegionIds} />
            <label class="form-check-label" for={"region-" + r.id}>
              {r.name}
            </label>
          </div>
        {/each}
      </div>
    </div>
  {/if}
</div>
        {#if success}
          <div class="alert alert-success">{success}</div>
        {/if}

        <div class="d-flex gap-2">
          <button type="submit" class="btn btn-primary" disabled={saving}>
            {#if saving}
              <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
              <span class="visually-hidden">Saving...</span>
            {/if}
            Save
          </button>
          <button type="button" class="btn btn-secondary" on:click={cancel} disabled={saving}>Cancel</button>
        </div>
      </form>
    {/if}
  {/if}
</div>

<style>
  /* keep the layout clean on small screens */
  .form-text { color: #6c757d; }
</style>
