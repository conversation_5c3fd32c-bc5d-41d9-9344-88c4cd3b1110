<script>
    import { onMount } from 'svelte';
    import { page } from '$app/stores';
    import { goto } from '$app/navigation';

    import CustomerCard from '$lib/components/CustomerCard.svelte';
    import CustomerNotes from '$lib/components/CustomerNotes.svelte';
	import CustomerEmails from '$lib/components/CustomerEmails.svelte';
	import CustomerEquipment from '$lib/components/CustomerEquipment.svelte';
    import CustomerVisits from '$lib/components/CustomerVisits.svelte';

    let customer = null;
    let loading = true;
    let error = '';

    function getId() {
        const params = $page?.params;
        if (params && params.id) return params.id;
        const parts = $page?.url?.pathname?.split('/').filter(Boolean) || [];
        return parts.length ? parts[parts.length - 1] : null;
    }

    async function loadCustomer() {
        loading = true;
        error = '';
        const id = getId();
        if (!id) {
            error = 'Missing customer id in URL';
            loading = false;
            return;
        }

        try {
            const res = await fetch(`/api/customers/${id}`);
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            let data = await res.json();
            if (typeof data === 'string') {
                try { data = JSON.parse(data); } catch (e) { /* ignore */ }
            }
            // API sometimes returns an array with one object — normalize to a single customer object
            if (Array.isArray(data)) {
                customer = data[0] ?? null;
            } else {
                customer = data ?? null;
            }
        } catch (err) {
            error = err?.message || String(err) || 'Unknown error';
            customer = null;
        } finally {
            loading = false;
        }
    }

    onMount(() => {
        loadCustomer();
    });

    function onEdit() {
        if (!customer) return;
        const id = customer.Id ?? customer.id;
        if (!id) return;
        goto(`/admins/CustomerEdit/${id}`);
    }
</script>

{#if loading}
    <div class="container mt-4">
        <div class="text-center">
            <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
        </div>
    </div>
{:else}
    <div class="container mt-4">
        <div class="mb-3">
            <button class="btn btn-primary" on:click={onEdit}>Edit Customer</button>
        </div>

        {#if error}
            <div class="alert alert-danger">{error}</div>
        {:else if customer}
            <CustomerCard {customer} />
            <br>
            <CustomerNotes {customer} />
            <br>
            <CustomerEmails {customer} />
            <br>
            <CustomerEquipment {customer} />
            <br>
            <CustomerVisits {customer} />
        {:else}
            <div class="alert alert-warning">Customer not found.</div>
        {/if}
    </div>
{/if}
