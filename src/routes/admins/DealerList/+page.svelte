<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';

  let dealers = [];
  let loading = true;
  let error = '';

  // sorting state
  let sortColumn = 'name';
  let sortDirection = 'asc';

  // Function to load region counts for all dealers
  async function loadRegionCounts() {
    // Create a map to store region counts for each dealer
    const regionCounts = new Map();

    // Fetch region counts for each dealer
    const promises = dealers.map(async (dealer) => {
      try {
        const res = await fetch(`/api/dealerregions/${dealer.id}`);
        if (res.ok) {
          let data = await res.json();
          if (!Array.isArray(data)) data = data?.results ?? data?.items ?? [data];
          const count = Array.isArray(data) ? data.length : 0;
          regionCounts.set(dealer.id, count);
        } else {
          // If 404 or other error, assume 0 regions
          regionCounts.set(dealer.id, 0);
        }
      } catch (err) {
        console.error(`Failed to load regions for dealer ${dealer.id}:`, err);
        regionCounts.set(dealer.id, 0);
      }
    });

    // Wait for all requests to complete
    await Promise.all(promises);

    // Update dealers array with actual region counts
    dealers = dealers.map(dealer => ({
      ...dealer,
      regions: regionCounts.get(dealer.id) ?? 0
    }));
  }

  onMount(async () => {
    loading = true;
    error = '';
    try {
      const res = await fetch('/api/dealers');
      if (!res.ok) throw new Error(`HTTP ${res.status}`);
      let data = await res.json();
      // normalize to array of dealers
      if (!Array.isArray(data)) {
        // if API returns container object, try to pick array
        data = data?.results ?? data?.items ?? [data];
      }
      // Transform API response data into normalized dealer objects
      // Handles various API response formats (PascalCase, camelCase, etc.)
      dealers = (data || []).map(d => {
        // Calculate region count instead of region names
        let regionCount = 0;
        if (Array.isArray(d.Regions)) {
          regionCount = d.Regions.length;
        } else if (d.RegionCount !== undefined) {
          regionCount = d.RegionCount;
        } else if (d.regionCount !== undefined) {
          regionCount = d.regionCount;
        }

        // Return normalized dealer object with consistent property names
        return {
          id: d.Id ?? d.id,
          loginId: d.LoginId ?? d.loginId ?? '',
          name: (d.FirstName ?? d.firstname ?? '') + ' ' + (d.LastName ?? d.lastname ?? ''),
          email: d.Email ?? d.email ?? '',
          phone: d.PhoneNumber ?? d.phoneNumber ?? d.Phone ?? d.phone ?? '',
          regions: regionCount // Now stores the count instead of names
        };
      });

      // Load region counts for each dealer from DealerRegions table
      await loadRegionCounts();
    } catch (err) {
      console.error(err);
      error = err.message || 'Failed to load dealers';
    } finally {
      loading = false;
    }
  });

  // derived sorted array
  $: sortedDealers = (() => {
    const arr = [...dealers];
    const dir = sortDirection === 'asc' ? 1 : -1;
    arr.sort((a, b) => {
      const A = (a[sortColumn] ?? '').toString().toLowerCase();
      const B = (b[sortColumn] ?? '').toString().toLowerCase();
      if (A < B) return -1 * dir;
      if (A > B) return 1 * dir;
      return 0;
    });
    return arr;
  })();

  function toggleSort(column) {
    if (sortColumn === column) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortColumn = column;
      sortDirection = 'asc';
    }
  }

  function headerKeyHandler(e, column) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      toggleSort(column);
    }
  }

  function editDealer(id) {
    if (!id) return;
    goto(`/admins/DealerEdit/${id}`);
  }
</script>

<div class="container mt-4">
  <h1>Dealer List</h1>

  {#if loading}
    <div class="text-center my-4">
      <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
    </div>
  {:else if error}
    <div class="alert alert-danger">{error}</div>
  {:else}
    {#if sortedDealers.length === 0}
      <div class="alert alert-info">No dealers found.</div>
    {:else}
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-light">
            <tr>
              <th scope="col" style="width:48px;" class="text-center" aria-label="Edit column"></th>
              <th scope="col" role="button" tabindex="0"
                  on:click={() => toggleSort('loginId')}
                  on:keydown={(e) => headerKeyHandler(e, 'loginId')}>
                Login ID {#if sortColumn==='loginId'}{sortDirection==='asc' ? '▲' : '▼'}{/if}
              </th>
              <th scope="col" role="button" tabindex="0"
                  on:click={() => toggleSort('name')}
                  on:keydown={(e) => headerKeyHandler(e, 'name')}>
                Name {#if sortColumn==='name'}{sortDirection==='asc' ? '▲' : '▼'}{/if}
              </th>
              <th scope="col" role="button" tabindex="0"
                  on:click={() => toggleSort('email')}
                  on:keydown={(e) => headerKeyHandler(e, 'email')}>
                Email {#if sortColumn==='email'}{sortDirection==='asc' ? '▲' : '▼'}{/if}
              </th>
              <th scope="col" role="button" tabindex="0"
                  on:click={() => toggleSort('phone')}
                  on:keydown={(e) => headerKeyHandler(e, 'phone')}>
                Phone Number {#if sortColumn==='phone'}{sortDirection==='asc' ? '▲' : '▼'}{/if}
              </th>
              <th scope="col" role="button" tabindex="0"
                  on:click={() => toggleSort('regions')}
                  on:keydown={(e) => headerKeyHandler(e, 'regions')}>
                Regions {#if sortColumn==='regions'}{sortDirection==='asc' ? '▲' : '▼'}{/if}
              </th>
            </tr>
          </thead>
          <tbody>
            {#each sortedDealers as dealer}
              <tr>
                <td class="text-center align-middle">
                  <button type="button" class="btn btn-link p-0" on:click={() => editDealer(dealer.id)} aria-label={"Edit dealer " + dealer.loginId} title="Edit">
                    <!-- simple pencil svg -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pencil" viewBox="0 0 16 16" aria-hidden="true">
                      <path d="M12.146.146a.5.5 0 0 1 .708 0l2.0 2a.5.5 0 0 1 0 .708l-9.193 9.193a.5.5 0 0 1-.168.11l-4 1.5a.5.5 0 0 1-.65-.65l1.5-4a.5.5 0 0 1 .11-.168L12.146.146zM11.207 2L3 10.207V13h2.793L14 4.793 11.207 2z"/>
                    </svg>
                  </button>
                </td>
                <td class="align-middle">{dealer.loginId}</td>
                <td class="align-middle">{dealer.name}</td>
                <td class="align-middle">{dealer.email}</td>
                <td class="align-middle">{dealer.phone}</td>
                <td class="align-middle">{dealer.regions}</td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  {/if}
</div>

<style>
  th[role="button"], th[role="button"]:hover {
    cursor: pointer;
    user-select: none;
  }
  button[title="Edit"] svg {
    vertical-align: middle;
  }
</style>

