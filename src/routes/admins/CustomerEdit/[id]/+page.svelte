<script>
    import { onMount } from 'svelte';
    import { page } from '$app/stores';
    import { goto } from '$app/navigation';

    let id;
    $: id = $page?.params?.id;

    let loading = true;
    let error = '';
    let saving = false;
    let success = '';

    // Customer form fields
    let name = '';
    let address1 = '';
    let address2 = '';
    let city = '';
    let state = '';
    let zipCode = '';
    let phoneNumber = '';
    let regionId = '';
    let notes = '';

    // Regions for dropdown
    let regions = [];
    let regionsLoading = true;
    let regionsError = '';

    function getId() {
        const params = $page?.params;
        if (params && params.id) return params.id;
        const parts = $page?.url?.pathname?.split('/').filter(Boolean) || [];
        return parts.length ? parts[parts.length - 1] : null;
    }

    async function loadRegions() {
        regionsLoading = true;
        regionsError = '';
        try {
            const res = await fetch('/api/regions');
            if (!res.ok) throw new Error(`Failed to load regions (${res.status})`);
            let data = await res.json();
            if (typeof data === 'string') {
                try { data = JSON.parse(data); } catch (e) {}
            }
            if (!Array.isArray(data)) data = data ? [data] : [];
            regions = data.map(r => ({ id: r.Id ?? r.id, name: r.Name ?? r.name }));
        } catch (err) {
            console.error('Failed to load regions', err);
            regionsError = err?.message ?? 'Failed to load regions';
            regions = [];
        } finally {
            regionsLoading = false;
        }
    }

    async function loadCustomer() {
        if (!id) return;
        loading = true;
        error = '';
        try {
            const res = await fetch(`/api/customers/${id}`);
            if (!res.ok) throw new Error(`Failed to fetch customer (${res.status})`);
            let data = await res.json();
            if (typeof data === 'string') {
                try { data = JSON.parse(data); } catch (e) { /* ignore */ }
            }
            // API sometimes returns an array with one object — normalize to a single customer object
            if (Array.isArray(data)) {
                data = data[0] ?? null;
            }
            if (!data) {
                error = 'Customer not found';
                return;
            }

            // Populate form fields with customer data
            name = data.Name ?? data.name ?? '';
            address1 = data.Address1 ?? data.address1 ?? '';
            address2 = data.Address2 ?? data.address2 ?? '';
            city = data.City ?? data.city ?? '';
            state = data.State ?? data.state ?? '';
            zipCode = data.ZipCode ?? data.zipCode ?? '';
            phoneNumber = data.PhoneNumber ?? data.phoneNumber ?? '';
            regionId = String(data.Region_Id ?? data.regionId ?? data.region_id ?? '');
            notes = data.Notes ?? data.notes ?? '';

        } catch (err) {
            console.error('Failed to load customer', err);
            error = err?.message ?? 'Failed to load customer';
        } finally {
            loading = false;
        }
    }

    onMount(() => {
        loadCustomer();
        loadRegions();
    });

    async function save() {
        if (!id) return;
        saving = true;
        error = '';
        success = '';

        const payload = {
            Name: name,
            Address1: address1,
            Address2: address2,
            City: city,
            State: state,
            ZipCode: zipCode,
            PhoneNumber: phoneNumber,
            Region_Id: regionId || undefined,
            Notes: notes
        };

        try {
            const res = await fetch(`/api/customers/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            if (!res.ok) {
                const text = await res.text();
                throw new Error(text || `Save failed (${res.status})`);
            }
            success = 'Customer saved successfully.';
            // Optionally navigate back after save:
            // goto('/admins/CustomerDetail/' + id);
        } catch (err) {
            console.error('Failed to save customer', err);
            error = err?.message ?? 'Save failed';
        } finally {
            saving = false;
        }
    }

    function cancel() {
        goto('/admins/CustomerDetail/' + id);
    }
</script>

<div class="container mt-4">
    <h1 class="mb-3">Edit Customer</h1>

    {#if loading}
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    {:else}
        {#if error}
            <div class="alert alert-danger">{error}</div>
        {/if}

        {#if success}
            <div class="alert alert-success">{success}</div>
        {/if}

        {#if !error}
            <form on:submit|preventDefault={save} class="needs-validation" novalidate>
                <div class="row">
                    <!-- Left column: Customer Details -->
                    <div class="col-12 col-md-6">
                        <div class="card mb-3 bg-light border-0">
                            <div class="card-header bg-secondary text-white">
                                Customer Details
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name *</label>
                                    <input
                                        id="name"
                                        type="text"
                                        class="form-control"
                                        bind:value={name}
                                        required
                                    />
                                </div>
                                <div class="mb-3">
                                    <label for="phoneNumber" class="form-label">Phone Number</label>
                                    <input
                                        id="phoneNumber"
                                        type="tel"
                                        class="form-control"
                                        bind:value={phoneNumber}
                                    />
                                </div>
                                <div class="mb-3">
                                    <label for="region" class="form-label">Region</label>
                                    {#if regionsLoading}
                                        <div class="spinner-border spinner-border-sm" role="status">
                                            <span class="visually-hidden">Loading regions...</span>
                                        </div>
                                    {:else if regionsError}
                                        <div class="text-danger small">{regionsError}</div>
                                        <select class="form-select" id="region" bind:value={regionId} disabled>
                                            <option value="">Unable to load regions</option>
                                        </select>
                                    {:else}
                                        <select class="form-select" id="region" bind:value={regionId}>
                                            <option value="">Select a region...</option>
                                            {#each regions as r}
                                                <option value={r.id}>{r.name}</option>
                                            {/each}
                                        </select>
                                    {/if}
                                </div>
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea
                                        id="notes"
                                        class="form-control"
                                        rows="4"
                                        bind:value={notes}
                                    ></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right column: Address -->
                    <div class="col-12 col-md-6">
                        <div class="card mb-3 bg-light border-0">
                            <div class="card-header bg-secondary text-white">
                                Address
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="address1" class="form-label">Address 1</label>
                                    <input
                                        id="address1"
                                        type="text"
                                        class="form-control"
                                        bind:value={address1}
                                    />
                                </div>
                                <div class="mb-3">
                                    <label for="address2" class="form-label">Address 2</label>
                                    <input
                                        id="address2"
                                        type="text"
                                        class="form-control"
                                        bind:value={address2}
                                    />
                                </div>
                                <div class="mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input
                                        id="city"
                                        type="text"
                                        class="form-control"
                                        bind:value={city}
                                    />
                                </div>
                                <div class="mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input
                                        id="state"
                                        type="text"
                                        class="form-control"
                                        bind:value={state}
                                    />
                                </div>
                                <div class="mb-3">
                                    <label for="zipCode" class="form-label">Zip Code</label>
                                    <input
                                        id="zipCode"
                                        type="text"
                                        class="form-control"
                                        bind:value={zipCode}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-4">
                    <button
                        type="submit"
                        class="btn btn-primary me-2"
                        disabled={saving}
                    >
                        {saving ? 'Saving...' : 'Save Customer'}
                    </button>
                    <button
                        type="button"
                        class="btn btn-secondary"
                        on:click={cancel}
                        disabled={saving}
                    >
                        Cancel
                    </button>
                </div>
            </form>
        {/if}
    {/if}
</div>