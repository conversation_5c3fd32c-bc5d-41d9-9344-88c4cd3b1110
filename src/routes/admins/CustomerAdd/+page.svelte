<script>
	import { onMount } from 'svelte';

	let regions = [];
	let regionId = '';

	async function loadRegions() {
		try {
			const res = await fetch('/api/regions');
			if (!res.ok) throw new Error(`HTTP ${res.status}`);
			let data = await res.json();
			if (typeof data === 'string') {
				try { data = JSON.parse(data); } catch (e) {}
			}
			if (!Array.isArray(data)) data = data ? [data] : [];
			regions = data.map(r => ({ id: r.Id ?? r.id, name: r.Name ?? r.name }));
		} catch (err) {
			console.error('Failed to load regions', err);
			regions = [];
		}
	}

	onMount(() => {
		loadRegions();
	});
</script>

<div class="container mt-4">
	<h1 class="mb-4">Add New Customer</h1>

	<form>
		<div class="row">
			<!-- Left column: Customer Details -->
			<div class="col-12 col-md-6">
				<div class="card mb-3 bg-light border-0">
					<div class="card-header bg-secondary text-white">
						Customer Details
					</div>
					<div class="card-body">
						<div class="mb-3">
							<label for="name" class="form-label">Name</label>
							<input type="text" class="form-control" id="name" />
						</div>
						<div class="mb-3">
							<label for="phoneNumber" class="form-label">Phone Number</label>
							<input type="tel" class="form-control" id="phoneNumber" />
						</div>
						<div class="mb-3">
							<label for="region" class="form-label">Region</label>
							<select class="form-select" id="region" bind:value={regionId}>
								<option value="">Select a region...</option>
								{#each regions as r}
									<option value={r.id}>{r.name}</option>
								{/each}
							</select>
						</div>
						<div class="mb-3">
							<label for="loginId" class="form-label">Login ID</label>
							<input type="text" class="form-control" id="loginId" />
						</div>
						<div class="mb-3">
							<label for="password" class="form-label">Password</label>
							<input type="password" class="form-control" id="password" />
						</div>
					</div>
				</div>
			</div>

			<!-- Right column: Address -->
			<div class="col-12 col-md-6">
				<div class="card mb-3 bg-light border-0">
					<div class="card-header bg-secondary text-white">
						Address
					</div>
					<div class="card-body">
						<div class="mb-3">
							<label for="address1" class="form-label">Address 1</label>
							<input type="text" class="form-control" id="address1" />
						</div>
						<div class="mb-3">
							<label for="address2" class="form-label">Address 2</label>
							<input type="text" class="form-control" id="address2" />
						</div>
						<div class="mb-3">
							<label for="city" class="form-label">City</label>
							<input type="text" class="form-control" id="city" />
						</div>
						<div class="mb-3">
							<label for="state" class="form-label">State</label>
							<input type="text" class="form-control" id="state" />
						</div>
						<div class="mb-3">
							<label for="zipCode" class="form-label">Zip Code</label>
							<input type="text" class="form-control" id="zipCode" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>

	<!-- Action Buttons -->
	<div class="mt-4">
		<button type="button" class="btn btn-primary me-2">Save</button>
		<button type="button" class="btn btn-danger">Cancel</button>
	</div>
</div>
