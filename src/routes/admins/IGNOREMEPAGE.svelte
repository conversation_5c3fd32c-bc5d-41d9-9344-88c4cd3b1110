<script>
    import { onMount } from 'svelte';
    import { page } from '$app/stores';
    import { goto } from '$app/navigation';

    let admins = [];
    let loading = true;
    let error = '';

    async function getAdmins() {
        loading = true;
        error = '';
        try {
            const response = await fetch('/api/admins');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status} - ${response.statusText}`);
            }
            // normalize server response into an array of objects with predictable keys
            let data = await response.json();
            // sometimes APIs return a JSON-string inside JSON; try to parse if needed
            if (typeof data === 'string') {
                try {
                    data = JSON.parse(data);
                } catch (e) {
                    // leave as-is
                }
            }
            if (!Array.isArray(data)) {
                // if the API returns a single object, wrap it
                data = data ? [data] : [];
            }

            // map server fields to consistent names used in the template
            admins = data.map((a) => ({
                id: a.Id ?? a.id ?? null,
                loginId: a.LoginId ?? a.LoginId ?? a.loginId ?? a.login ?? '',
                password: a.Password ?? a.password ?? a.Pass ?? '',
                email: a.Email ?? a.email ?? ''
            }));
            console.debug('Fetched and normalized admins:', admins);
        } catch (err) {
            console.error('Failed to fetch admins:', err);
            error = err?.message || String(err) || 'Unknown error';
            admins = [];
        } finally {
            loading = false;
        }
    }

    onMount(() => {
        getAdmins();
    });

</script>

<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">Admins</h3>
        <div>
            <button class="btn btn-sm btn-primary me-2" on:click={getAdmins} disabled={loading}>
                {#if loading}
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Refreshing...
                {:else}
                    Refresh
                {/if}
            </button>
        </div>
    </div>

    {#if error}
        <div class="alert alert-danger" role="alert">
            Error loading admins: {error} <button class="btn btn-sm btn-link" on:click={getAdmins}>Retry</button>
        </div>
    {/if}

    <table class="table table-striped table-hover">
        <thead class="table-light">
            <tr>
                <th>Login ID</th>
                <th>Password</th>
                <th>Email</th>
            </tr>
        </thead>
        <tbody>
            {#if loading}
                <tr>
                    <td colspan="3">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border me-2" role="status" aria-hidden="true"></div>
                            Loading admins…
                        </div>
                    </td>
                </tr>
            {:else if admins.length === 0}
                <tr>
                    <td colspan="3">No admins found.</td>
                </tr>
            {:else}
                {#each admins as admin (admin.id)}
                    <tr>
                        <td>{admin.loginId}</td>
                        <td>{admin.password}</td>
                        <td>{admin.email}</td>
                    </tr>
                {/each}
            {/if}
        </tbody>
    </table>
</div>
