<script>
    import { onMount } from 'svelte';
    import { page } from '$app/stores';
    import { goto } from '$app/navigation';

    let id = null;
    let customerName = '';
    let loading = true;
    let error = '';

    let noteBody = '';
    let submitting = false;
    let success = '';

    $: id = $page.params.id;

    async function fetchCustomer() {
        if (!id) return;
        loading = true;
        error = '';
        try {
            const res = await fetch(`/api/customers/${id}`);
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            let data = await res.json();
            if (Array.isArray(data)) data = data[0] ?? null;
            customerName = data?.Name ?? data?.name ?? '';
        } catch (err) {
            console.error('Failed to fetch customer', err);
            error = err?.message || String(err) || 'Failed to load customer';
        } finally {
            loading = false;
        }
    }

    onMount(() => {
        fetchCustomer();
    });

    async function submitNote() {
        if (!noteBody.trim()) {
            error = 'Please enter a note.';
            return;
        }
        submitting = true;
        error = '';
        success = '';
        try {
            const res = await fetch(`/api/customers/${id}/notes`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ Body: noteBody })
            });
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            success = 'Note added.';
            noteBody = '';
        } catch (err) {
            console.error('Failed to submit note', err);
            error = err?.message || String(err) || 'Failed to save note';
        } finally {
            submitting = false;
        }
    }

    function cancel() {
        goto('/admins/CustomerDetail/' + id);
    }
</script>

<div class="container mt-4">
    {#if loading}
        <div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>
    {:else}
        {#if error}
            <div class="alert alert-danger">{error}</div>
        {/if}

        <h1>Add New Note to {customerName}</h1>

        <div class="mb-3">
            <label for="noteBody" class="form-label">Note</label>
            <textarea id="noteBody" class="form-control" rows="7" bind:value={noteBody} />
        </div>

        <div class="mb-3">
            <button class="btn btn-primary me-2" on:click={submitNote} disabled={submitting}>{submitting ? 'Saving...' : 'Save Note'}</button>
            <button class="btn btn-secondary" on:click={cancel} disabled={submitting}>Cancel</button>
        </div>

        {#if success}
            <div class="alert alert-success">{success}</div>
        {/if}
    {/if}
</div>

<style>
    textarea.form-control { font-size: 1rem; }
</style>
