<script>
    import { onMount } from 'svelte';

    let regions = $state([]);
    let loading = $state(true);
    let error = $state('');

    async function fetchRegions() {
        try {
            console.log('Fetching regions from /api/regions/list...');
            const res = await fetch('/api/regions/list');
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            const data = await res.json();
            console.log('Raw regions data:', data);

            regions = data;
            console.log('Loaded regions:', regions.length);
        } catch (err) {
            console.error('Failed to fetch regions:', err);
            error = `Failed to load regions: ${err.message}`;
        } finally {
            loading = false;
        }
    }

    onMount(() => {
        fetchRegions();
    });
</script>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Region List</h1>
        <a href="/admins/RegionAdd" class="btn btn-primary">Add New Region</a>
    </div>

    {#if loading}
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    {:else if error}
        <div class="alert alert-danger" role="alert">
            {error}
        </div>
    {:else}
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Dealers</th>
                    <th>Customers</th>
                </tr>
            </thead>
            <tbody>
                {#each regions as region}
                    <tr>
                        <td>{region.RegionName}</td>
                        <td>{region.Dealers}</td>
                        <td>{region.Customers}</td>
                    </tr>
                {/each}
            </tbody>
        </table>
    {/if}
</div>