<script>
    import { onMount } from 'svelte';
    import { page } from '$app/stores';
    import { goto } from '$app/navigation';
	import { Name } from 'drizzle-orm';

    import CustomerModal from '$lib/components/CustomerModal.svelte';

    let customers = [];
    let loading = true;
    let error = '';
    let modalOpen = false;
    let selectedCustomer = null;
    // sorting state
    let sortColumn = 'name'; // default sort
    let sortDirection = 'asc'; // 'asc' or 'desc'

    async function GetCustomerList() {
        loading = true;
        error = '';
        try {
            const response = await fetch('/api/customerlist');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status} - ${response.statusText}`);
            }
            // normalize server response into an array of objects with predictable keys
            let data = await response.json();
            // sometimes APIs return a JSON-string inside JSON; try to parse if needed
            if (typeof data === 'string') {
                try {
                    data = JSON.parse(data);
                } catch (e) {
                    // leave as-is
                }
            }
            if (!Array.isArray(data)) {
                // if the API returns a single object, wrap it
                data = data ? [data] : [];
            }

            // map server fields to consistent names used in the template
            customers = data.map((a) => ({
                id: a.Id ?? a.id ?? null,
                name: a.Name || a.name || '',
                regionName: a.RegionName || a.region_name || a.regionName || '',
                phoneNumber: a.PhoneNumber || a.phone_number || a.phoneNumber || '',
                mostRecentReading: a.MostRecentReading || a.most_recent_reading || a.mostRecentReading || ''    
            }));
            console.debug('Fetched and normalized customers:', customers);
        } catch (err) {
            console.error('Failed to fetch customers:', err);
            error = err?.message || String(err) || 'Unknown error';
            customers = [];
        } finally {
            loading = false;
        }
    }

    onMount(() => {
        GetCustomerList();
    });

    // derived sorted list
    $: sortedCustomers = [...customers].sort((a, b) => {
        const col = sortColumn;
        const va = (a[col] ?? '').toString().toLowerCase();
        const vb = (b[col] ?? '').toString().toLowerCase();
        if (va === vb) return 0;
        if (sortDirection === 'asc') return va < vb ? -1 : 1;
        return va < vb ? 1 : -1;
    });

    function toggleSort(column) {
        if (sortColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }
    }

    async function openCustomerModal(id) {
        try {
            const res = await fetch(`/api/customers/${id}`);
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            let data = await res.json();
            if (Array.isArray(data)) data = data[0] ?? null;
            selectedCustomer = data;
            modalOpen = true;
        } catch (err) {
            console.error('Failed to load customer for modal', err);
        }
    }

</script>

<div class="container mt-4">
    <h1>Customer List</h1>
    <a href="/admins/CustomerAdd" class="btn btn-primary mb-3">Add New Customer</a>
    {#if loading}
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    {:else if error}
        <div class="alert alert-danger" role="alert">
            {error}
        </div>
    {:else}
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>Actions</th>
                    <th tabindex="0" role="button" on:click={() => toggleSort('name')} on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); toggleSort('name'); } }}>Name {#if sortColumn === 'name'}{sortDirection === 'asc' ? '▲' : '▼'}{/if}</th>
                    <th tabindex="0" role="button" on:click={() => toggleSort('regionName')} on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); toggleSort('regionName'); } }}>Region {#if sortColumn === 'regionName'}{sortDirection === 'asc' ? '▲' : '▼'}{/if}</th>
                    <th tabindex="0" role="button" on:click={() => toggleSort('phoneNumber')} on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); toggleSort('phoneNumber'); } }}>Phone Number {#if sortColumn === 'phoneNumber'}{sortDirection === 'asc' ? '▲' : '▼'}{/if}</th>
                    <th tabindex="0" role="button" on:click={() => toggleSort('mostRecentReading')} on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); toggleSort('mostRecentReading'); } }}>Last Reading {#if sortColumn === 'mostRecentReading'}{sortDirection === 'asc' ? '▲' : '▼'}{/if}</th>
                </tr>
            </thead>
            <tbody>
                {#each sortedCustomers as customer}
                    <tr>
                        <td>
                            <button type="button" class="btn btn-sm btn-info" title="View" on:click={() => goto(`/admins/CustomerDetail/${customer.id}`)}>
                                <i class="bi bi-eye"></i>
                            </button>
                     
                            <button class="btn btn-sm btn-warning" title="Edit">
                                <i class="bi bi-pencil"></i>
                            </button>
                      
                            <button class="btn btn-sm btn-danger" title="Delete">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                        <td role="button" tabindex="0" class="clickable-name" on:click={() => openCustomerModal(customer.id)} on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); openCustomerModal(customer.id); } }}>
                            {customer.name}
                        </td>
                        <td>{customer.regionName}</td>
                        <td>{customer.phoneNumber}</td>
                        <td>{customer.mostRecentReading}</td>
                    </tr>
                {/each}
            </tbody>
        </table>
        <CustomerModal {selectedCustomer} customer={selectedCustomer} open={modalOpen} on:close={() => { modalOpen = false; selectedCustomer = null; }} />
    {/if}
</div>

<style>
    td[role="button"].clickable-name { cursor: pointer; }
</style>

