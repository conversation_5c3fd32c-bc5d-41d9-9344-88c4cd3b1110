<script>
    import { onMount } from 'svelte';
    import CustomersNew from '$lib/components/CustomersNew.svelte';
    let visits = $state([]);
    let loading = $state(true);
    let error = $state('');
    let currentPage = $state(1);
    let totalPages = $state(0);
    let totalVisits = $state(0);

    const visitsPerPage = 15;

    // Computed values for pagination - using a different approach
    let paginatedVisits = $state([]);

    // Update paginated visits when visits or currentPage changes
    $effect(() => {
        if (visits && visits.length > 0) {
            const startIndex = (currentPage - 1) * visitsPerPage;
            const endIndex = startIndex + visitsPerPage;
            paginatedVisits = visits.slice(startIndex, endIndex);
        } else {
            paginatedVisits = [];
        }
    });

    let pageNumbers = $derived(() => {
        const pages = [];
        const maxPagesToShow = 10;
        let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

        // Adjust start page if we're near the end
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(1, endPage - maxPagesToShow + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            pages.push(i);
        }
        return pages;
    });

    async function fetchVisits() {
        try {
            console.log('Fetching visits from /api/dashboard...');
            const res = await fetch('/api/dashboard');
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            const data = await res.json();
            console.log('Raw visits data length:', data.length);

            // Sort by visit date (most recent first) and take only the most recent 30
            const sortedData = data.sort((a, b) => new Date(b.VisitDate) - new Date(a.VisitDate));
            const recentVisits = sortedData.slice(0, 30);

            visits = recentVisits;
            totalVisits = recentVisits.length;
            totalPages = Math.ceil(totalVisits / visitsPerPage);
            console.log('Loaded recent visits:', visits.length, 'Total pages:', totalPages);
        } catch (err) {
            console.error('Failed to fetch visits:', err);
            error = `Failed to load visits: ${err.message}`;
        } finally {
            loading = false;
        }
    }

    function goToPage(page) {
        if (page >= 1 && page <= totalPages) {
            currentPage = page;
        }
    }

    function formatDate(dateString) {
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }

    onMount(() => {
        fetchVisits();
    });
</script>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Most Recent Visits</h1>
    </div>

    {#if loading}
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    {:else if error}
        <div class="alert alert-danger" role="alert">
            {error}
        </div>
    {:else}
        <!-- Summary info -->
        <div class="mb-3">
            <small class="text-muted">
                Showing {((currentPage - 1) * visitsPerPage) + 1} to {Math.min(currentPage * visitsPerPage, totalVisits)} of {totalVisits} visits
            </small>
        </div>



        <!-- Visits table -->
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th style="width: 50px;"></th>
                    <th>Visit Date</th>
                    <th>Customer</th>
                    <th>Dealer</th>
                    <th>Comments</th>
                </tr>
            </thead>
            <tbody>
                {#each paginatedVisits as visit}
                    <tr>
                        <td>
                            <a href="/admins/Visit/{visit.Id}" class="text-primary" aria-label="View visit details for {visit.Customer_Name}">
                                <i class="fas fa-search" title="View Visit Details"></i>
                            </a>
                        </td>
                        <td>{formatDate(visit.VisitDate)}</td>
                        <td>{visit.Customer_Name}</td>
                        <td>{visit.Dealer_Name}</td>
                        <td>
                            {#if visit.Comments}
                                <span title="{visit.Comments}">
                                    {visit.Comments.length > 100 ? visit.Comments.substring(0, 100) + '...' : visit.Comments}
                                </span>
                            {:else}
                                <span class="text-muted">No comments</span>
                            {/if}
                        </td>
                    </tr>
                {/each}
            </tbody>
        </table>

        <!-- Pagination -->
        {#if totalPages > 1}
            <nav aria-label="Visit pagination">
                <ul class="pagination justify-content-center">
                    <!-- Previous button -->
                    <li class="page-item {currentPage === 1 ? 'disabled' : ''}">
                        <button class="page-link" onclick={() => goToPage(currentPage - 1)} disabled={currentPage === 1}>
                            Previous
                        </button>
                    </li>

                    <!-- First page -->
                    {#if pageNumbers[0] > 1}
                        <li class="page-item">
                            <button class="page-link" onclick={() => goToPage(1)}>1</button>
                        </li>
                        {#if pageNumbers[0] > 2}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {/if}
                    {/if}

                    <!-- Page numbers -->
                    {#each pageNumbers as pageNum}
                        <li class="page-item {currentPage === pageNum ? 'active' : ''}">
                            <button class="page-link" onclick={() => goToPage(pageNum)}>
                                {pageNum}
                            </button>
                        </li>
                    {/each}

                    <!-- Last page -->
                    {#if pageNumbers[pageNumbers.length - 1] < totalPages}
                        {#if pageNumbers[pageNumbers.length - 1] < totalPages - 1}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {/if}
                        <li class="page-item">
                            <button class="page-link" onclick={() => goToPage(totalPages)}>{totalPages}</button>
                        </li>
                    {/if}

                    <!-- Next button -->
                    <li class="page-item {currentPage === totalPages ? 'disabled' : ''}">
                        <button class="page-link" onclick={() => goToPage(currentPage + 1)} disabled={currentPage === totalPages}>
                            Next
                        </button>
                    </li>
                </ul>
            </nav>

            <CustomersNew/>

        {/if}
    {/if}
</div>