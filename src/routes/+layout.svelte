<script>
	// Prefer using the static copy so it can be referenced at '/rsi-logo.png'
	let { children } = $props();
	const logoSrc = '/rsi-logo.png';
</script>

<svelte:head>
		
		<!-- Navbar tweaks -->
		<style>
				.navbar-brand img { height: 100px; }
		</style>
</svelte:head>

<!-- Bootstrap navbar -->
<nav class="navbar navbar-expand-lg navbar-light bg-light">
	<div class="container-fluid">
		<a class="navbar-brand" href="/">
			<img src={logoSrc} alt="RSI Logo" />
		</a>
		<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
			<span class="navbar-toggler-icon"></span>
		</button>
		<div class="collapse navbar-collapse" id="navbarNav">
			<ul class="navbar-nav">
				<li class="nav-item">
					<a class="nav-link" href="/">Home</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="/admins/CustomerList">Customers</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="/admins/DealerList">Dealers</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="/admins/RegionList">Regions</a>
				</li>
			</ul>
		</div>
	</div>
</nav>

{@render children()}